{"name": "gregsithole-react-portfolio", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "axios": "^1.11.0", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "ogl": "^1.0.11", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-scripts": "5.0.1", "react-tabs": "^6.1.0", "react-toastify": "^11.0.5", "swiper": "^11.2.10", "use-local-storage": "^3.0.0", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}